2025-08-13 13:47:55,379 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for long_call_otm in 1min
2025-08-13 13:47:55,380 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 1min timeframe... 🤖
2025-08-13 13:47:55,380 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:47:55,380 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for long_call_otm in 1min... ⭐
2025-08-13 13:47:55,380 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'long_call_otm' (1min)... 📊
2025-08-13 13:47:55,380 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:47:55,382 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:47:55,382 - agents.options_signal_generation_agent - INFO - [FILTER] Signal long_call_otm filtered out due to low confidence (0.42) after regime check. 📉  
2025-08-13 13:47:55,382 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:47:55,382 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:47:55,382 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'long_call_otm' in 1min. 🗑️
2025-08-13 13:47:55,383 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'long_put_otm' for 1min timeframe... 🔍
2025-08-13 13:47:55,383 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for long_put_otm in 1min...
2025-08-13 13:47:55,383 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for long_put_otm - generating simple signals
2025-08-13 13:47:55,383 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for long_put_otm in 1min
2025-08-13 13:47:55,383 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 1min timeframe... 🤖
2025-08-13 13:47:55,384 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:47:55,384 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for long_put_otm in 1min... ⭐
2025-08-13 13:47:55,384 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'long_put_otm' (1min)... 📊
2025-08-13 13:47:55,384 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:47:55,385 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:47:55,386 - agents.options_signal_generation_agent - INFO - [FILTER] Signal long_put_otm filtered out due to low confidence (0.12) after regime check. 📉   
2025-08-13 13:47:55,386 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:47:55,386 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:47:55,386 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'long_put_otm' in 1min. 🗑️
2025-08-13 13:47:55,386 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'long_straddle' for 1min timeframe... 🔍
2025-08-13 13:47:55,386 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for long_straddle in 1min...
2025-08-13 13:47:55,387 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for long_straddle - generating simple signals
2025-08-13 13:47:55,387 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for long_straddle in 1min
2025-08-13 13:47:55,387 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 1min timeframe... 🤖
2025-08-13 13:47:55,387 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:47:55,387 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for long_straddle in 1min... ⭐
2025-08-13 13:47:55,388 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'long_straddle' (1min)... 📊
2025-08-13 13:47:55,388 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:47:55,389 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:47:55,389 - agents.options_signal_generation_agent - INFO - [FILTER] Signal long_straddle filtered out due to low confidence (0.42) after regime check. 📉  
2025-08-13 13:47:55,389 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:47:55,389 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:47:55,390 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'long_straddle' in 1min. 🗑️
2025-08-13 13:47:55,390 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'long_strangle' for 1min timeframe... 🔍
2025-08-13 13:47:55,390 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for long_strangle in 1min...
2025-08-13 13:47:55,390 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for long_strangle - generating simple signals
2025-08-13 13:47:55,390 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for long_strangle in 1min
2025-08-13 13:47:55,391 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 1min timeframe... 🤖
2025-08-13 13:47:55,391 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:47:55,391 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for long_strangle in 1min... ⭐
2025-08-13 13:47:55,391 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'long_strangle' (1min)... 📊
2025-08-13 13:47:55,391 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:47:55,393 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:47:55,393 - agents.options_signal_generation_agent - INFO - [FILTER] Signal long_strangle filtered out due to low confidence (0.42) after regime check. 📉  
2025-08-13 13:47:55,393 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:47:55,393 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:47:55,393 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'long_strangle' in 1min. 🗑️
2025-08-13 13:47:55,394 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'bull_call_spread' for 1min timeframe... 🔍
2025-08-13 13:47:55,394 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for bull_call_spread in 1min...
2025-08-13 13:47:55,394 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for bull_call_spread - generating simple signals
2025-08-13 13:47:55,394 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for bull_call_spread in 1min
2025-08-13 13:47:55,394 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 1min timeframe... 🤖
2025-08-13 13:47:55,395 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:47:55,395 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for bull_call_spread in 1min... ⭐
2025-08-13 13:47:55,395 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'bull_call_spread' (1min)... 📊
2025-08-13 13:47:55,395 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:47:55,397 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:47:55,397 - agents.options_signal_generation_agent - INFO - [FILTER] Signal bull_call_spread filtered out due to low confidence (0.42) after regime check. ��
2025-08-13 13:47:55,397 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:47:55,397 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:47:55,397 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'bull_call_spread' in 1min. 🗑️
2025-08-13 13:47:55,397 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'bear_put_spread' for 1min timeframe... 🔍
2025-08-13 13:47:55,398 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for bear_put_spread in 1min...
2025-08-13 13:47:55,398 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for bear_put_spread - generating simple signals
2025-08-13 13:47:55,398 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for bear_put_spread in 1min
2025-08-13 13:47:55,398 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 1min timeframe... 🤖
2025-08-13 13:47:55,399 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:47:55,399 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for bear_put_spread in 1min... ⭐
2025-08-13 13:47:55,399 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'bear_put_spread' (1min)... 📊
2025-08-13 13:47:55,399 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:47:55,400 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:47:55,401 - agents.options_signal_generation_agent - INFO - [FILTER] Signal bear_put_spread filtered out due to low confidence (0.42) after regime check. 📉
2025-08-13 13:47:55,401 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:47:55,401 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:47:55,401 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'bear_put_spread' in 1min. 🗑️
2025-08-13 13:47:55,401 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'iron_condor' for 1min timeframe... 🔍
2025-08-13 13:47:55,401 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for iron_condor in 1min...
2025-08-13 13:47:55,402 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for iron_condor - generating simple signals
2025-08-13 13:47:55,402 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for iron_condor in 1min
2025-08-13 13:47:55,402 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 1min timeframe... 🤖
2025-08-13 13:47:55,402 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:47:55,402 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for iron_condor in 1min... ⭐
2025-08-13 13:47:55,403 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'iron_condor' (1min)... 📊
2025-08-13 13:47:55,403 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:47:55,404 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:47:55,404 - agents.options_signal_generation_agent - INFO - [FILTER] Signal iron_condor filtered out due to low confidence (0.42) after regime check. 📉    
2025-08-13 13:47:55,404 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:47:55,404 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:47:55,405 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'iron_condor' in 1min. 🗑️
2025-08-13 13:47:55,405 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'short_straddle' for 1min timeframe... 🔍
2025-08-13 13:47:55,405 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for short_straddle in 1min...
2025-08-13 13:47:55,405 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for short_straddle - generating simple signals
2025-08-13 13:47:55,405 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for short_straddle in 1min
2025-08-13 13:47:55,406 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 1min timeframe... 🤖
2025-08-13 13:47:55,406 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:47:55,406 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for short_straddle in 1min... ⭐
2025-08-13 13:47:55,406 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'short_straddle' (1min)... 📊
2025-08-13 13:47:55,406 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:47:55,408 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:47:55,408 - agents.options_signal_generation_agent - INFO - [FILTER] Signal short_straddle filtered out due to low confidence (0.42) after regime check. 📉 
2025-08-13 13:47:55,408 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:47:55,408 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:47:55,408 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'short_straddle' in 1min. 🗑️
2025-08-13 13:47:55,409 - agents.options_signal_generation_agent - INFO - [STRATEGY] Completed 20 strategy tasks for 1min timeframe
2025-08-13 13:47:55,409 - agents.options_signal_generation_agent - INFO - [SIGNAL] Total signals generated across all timeframes: 8 📈
2025-08-13 13:47:55,410 - agents.options_signal_generation_agent - INFO - [SIGNAL] Filtered to BUY signals only: 0 🛒
2025-08-13 13:47:55,410 - agents.options_signal_generation_agent - INFO - [MULTI-TF] Applying multi-timeframe confirmation logic... 🕰️
2025-08-13 13:47:55,410 - agents.options_signal_generation_agent - INFO - [SIGNAL] Final signals after multi-timeframe confirmation: 0 ✅
2025-08-13 13:47:55,410 - agents.options_signal_generation_agent - INFO - [COORDINATION] Checking for multi-agent overrides/suggestions... 🤝
2025-08-13 13:47:55,410 - agents.options_signal_generation_agent - INFO - [SIGNAL] Final signals after agent overrides: 0 🤝
2025-08-13 13:47:55,410 - agents.options_signal_generation_agent - INFO - [SIGNAL] No final consolidated signals after all filtering. 🗑️
2025-08-13 13:47:55,411 - agents.options_signal_generation_agent - INFO - [EXECUTION] ✅ Signal generation cycle completed in 14.47s
2025-08-13 13:47:55,411 - agents.options_signal_generation_agent - INFO - [EXECUTION] 📊 Buy signals generated: 0
2025-08-13 13:47:55,411 - agents.options_signal_generation_agent - INFO - [EXECUTION] ⏰ Next cycle in 10s
2025-08-13 13:47:55,411 - agents.options_signal_generation_agent - INFO - [SUCCESS] All signal generation tasks completed successfully ✅
2025-08-13 13:47:55,411 - agents.options_signal_generation_agent - INFO - [CONTINUOUS] Waiting 10 seconds before next cycle... ⏳
2025-08-13 13:47:58,380 - agents.options_market_monitoring_agent - INFO - [OPTIONS] Extracting 1min option chain intelligence...
2025-08-13 13:47:58,380 - agents.options_market_monitoring_agent - INFO - [ANOMALY] Checking for anomalies...
2025-08-13 13:47:58,380 - agents.options_market_monitoring_agent - INFO - [MONITOR] Option chain monitoring loop active (integrated).
2025-08-13 13:47:58,380 - agents.options_market_monitoring_agent - INFO - [GREEKS] Monitoring 1min Greeks...
2025-08-13 13:47:58,380 - agents.options_market_monitoring_agent - INFO - ✅ [RISK SIGNAL] Market conditions are 'risk-on'.
2025-08-13 13:47:58,381 - agents.options_market_monitoring_agent - INFO - [ALERT] Checking multi-timeframe alerts...
2025-08-13 13:47:58,381 - agents.options_market_monitoring_agent - INFO - [REGIME] Detecting 1min market regime...
2025-08-13 13:47:58,392 - agents.options_market_monitoring_agent - INFO - [MONITOR] Monitoring 1min data...
2025-08-13 13:47:58,392 - agents.options_market_monitoring_agent - INFO - [PATTERN] Detecting 1min intraday patterns...
2025-08-13 13:48:05,414 - agents.options_signal_generation_agent - INFO - [SIGNAL] 🚀 Starting signal generation cycle at 13:48:05...
2025-08-13 13:48:05,414 - agents.options_signal_generation_agent - INFO - [EXECUTION] Signal generation agent running - Interval: 10s
2025-08-13 13:48:05,414 - agents.options_signal_generation_agent - INFO - [SIGNAL] Processing 1min timeframe... ⏱️
2025-08-13 13:48:07,046 - agents.options_signal_generation_agent - INFO - [SIGNAL] Processing 3min timeframe... ⏱️
2025-08-13 13:48:08,847 - agents.options_signal_generation_agent - INFO - [SIGNAL] Processing 5min timeframe... ⏱️
2025-08-13 13:48:10,438 - agents.options_signal_generation_agent - INFO - [SIGNAL] Processing 15min timeframe... ⏱️
2025-08-13 13:48:19,524 - agents.options_signal_generation_agent - INFO - [REGIME] Fetching current market regime information... 🌍
2025-08-13 13:48:19,524 - agents.options_signal_generation_agent - INFO - [STRATEGY] Starting 20 strategy tasks for 3min timeframe...
2025-08-13 13:48:19,525 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'volatility_breakout_ce' for 3min timeframe... 🔍
2025-08-13 13:48:19,525 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for volatility_breakout_ce in 3min...
2025-08-13 13:48:19,526 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for volatility_breakout_ce in 3min
2025-08-13 13:48:19,527 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'volatility_breakout_ce' in 3min. 🤷  
2025-08-13 13:48:19,527 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'volatility_breakout_pe' for 3min timeframe... 🔍
2025-08-13 13:48:19,527 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for volatility_breakout_pe in 3min...
2025-08-13 13:48:19,528 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for volatility_breakout_pe in 3min
2025-08-13 13:48:19,528 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'volatility_breakout_pe' in 3min. 🤷  
2025-08-13 13:48:19,529 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'momentum_long_call' for 3min timeframe... 🔍    
2025-08-13 13:48:19,529 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for momentum_long_call in 3min...
2025-08-13 13:48:19,530 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for momentum_long_call in 3min    
2025-08-13 13:48:19,530 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'momentum_long_call' in 3min. 🤷      
2025-08-13 13:48:19,530 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'momentum_long_put' for 3min timeframe... 🔍     
2025-08-13 13:48:19,530 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for momentum_long_put in 3min...
2025-08-13 13:48:19,532 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for momentum_long_put in 3min     
2025-08-13 13:48:19,532 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'momentum_long_put' in 3min. 🤷       
2025-08-13 13:48:19,532 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'oversold_bounce' for 3min timeframe... 🔍       
2025-08-13 13:48:19,532 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for oversold_bounce in 3min...
2025-08-13 13:48:19,534 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for oversold_bounce in 3min       
2025-08-13 13:48:19,534 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'oversold_bounce' in 3min. 🤷
2025-08-13 13:48:19,534 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'overbought_fade' for 3min timeframe... 🔍       
2025-08-13 13:48:19,534 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for overbought_fade in 3min...
2025-08-13 13:48:19,535 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for overbought_fade in 3min       
2025-08-13 13:48:19,536 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'overbought_fade' in 3min. 🤷
2025-08-13 13:48:19,536 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'unusual_volume_ce' for 3min timeframe... 🔍     
2025-08-13 13:48:19,536 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for unusual_volume_ce in 3min...
2025-08-13 13:48:19,537 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for unusual_volume_ce in 3min     
2025-08-13 13:48:19,537 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'unusual_volume_ce' in 3min. 🤷       
2025-08-13 13:48:19,538 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'unusual_volume_pe' for 3min timeframe... 🔍     
2025-08-13 13:48:19,538 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for unusual_volume_pe in 3min...
2025-08-13 13:48:19,539 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for unusual_volume_pe in 3min 
2025-08-13 13:48:19,539 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'unusual_volume_pe' in 3min. 🤷   
2025-08-13 13:48:19,540 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'trend_following_ce' for 3min timeframe... 🔍
2025-08-13 13:48:19,540 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for trend_following_ce in 3min...
2025-08-13 13:48:19,541 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 signals for trend_following_ce in 3min
2025-08-13 13:48:19,541 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 3min timeframe... 🤖
2025-08-13 13:48:19,542 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,542 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for trend_following_ce in 3min... ⭐       
2025-08-13 13:48:19,542 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'trend_following_ce' (3min)... 📊
2025-08-13 13:48:19,542 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,544 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,544 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,544 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,544 - agents.options_signal_generation_agent - INFO - [SIGNAL] Generated 1 final signals for strategy 'trend_following_ce' in 3min 🎉      
2025-08-13 13:48:19,544 - agents.options_signal_generation_agent - INFO - [LLM] Generating natural language signal summary... 📝
2025-08-13 13:48:19,545 - agents.options_signal_generation_agent - INFO - [SUMMARY] Example Signal Summary: BANKNIFTY 0 CE is a high-confidence BUY CE signal due to various technical factors. Strategy 'trend_following_ce' is active. 🗣️
2025-08-13 13:48:19,545 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'trend_following_pe' for 3min timeframe... 🔍
2025-08-13 13:48:19,545 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for trend_following_pe in 3min...
2025-08-13 13:48:19,547 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 signals for trend_following_pe in 3min
2025-08-13 13:48:19,547 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 3min timeframe... 🤖
2025-08-13 13:48:19,547 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,547 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for trend_following_pe in 3min... ⭐       
2025-08-13 13:48:19,547 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'trend_following_pe' (3min)... 📊
2025-08-13 13:48:19,548 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,549 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,549 - agents.options_signal_generation_agent - INFO - [FILTER] Signal trend_following_pe filtered out due to low confidence (0.25) after regime check. 📉
2025-08-13 13:48:19,549 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,549 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,550 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'trend_following_pe' in 3min. 🗑️      
2025-08-13 13:48:19,550 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'simple_call_signal' for 3min timeframe... 🔍
2025-08-13 13:48:19,550 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for simple_call_signal in 3min...
2025-08-13 13:48:19,550 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for simple_call_signal - generating simple signals
2025-08-13 13:48:19,550 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for simple_call_signal in 3min
2025-08-13 13:48:19,551 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 3min timeframe... 🤖
2025-08-13 13:48:19,551 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,551 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for simple_call_signal in 3min... ⭐       
2025-08-13 13:48:19,551 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'simple_call_signal' (3min)... 📊
2025-08-13 13:48:19,551 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,552 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,553 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,553 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,553 - agents.options_signal_generation_agent - INFO - [SIGNAL] Generated 1 final signals for strategy 'simple_call_signal' in 3min 🎉      
2025-08-13 13:48:19,553 - agents.options_signal_generation_agent - INFO - [LLM] Generating natural language signal summary... 📝
2025-08-13 13:48:19,553 - agents.options_signal_generation_agent - INFO - [SUMMARY] Example Signal Summary: BANKNIFTY 0 CE is a high-confidence BUY CE signal due to various technical factors. Strategy 'simple_call_signal' is active. 🗣️
2025-08-13 13:48:19,554 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'simple_put_signal' for 3min timeframe... 🔍
2025-08-13 13:48:19,554 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for simple_put_signal in 3min...
2025-08-13 13:48:19,554 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for simple_put_signal - generating simple signals
2025-08-13 13:48:19,554 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for simple_put_signal in 3min
2025-08-13 13:48:19,555 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 3min timeframe... 🤖
2025-08-13 13:48:19,555 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,555 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for simple_put_signal in 3min... ⭐        
2025-08-13 13:48:19,555 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'simple_put_signal' (3min)... 📊
2025-08-13 13:48:19,555 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,557 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,557 - agents.options_signal_generation_agent - INFO - [FILTER] Signal simple_put_signal filtered out due to low confidence (0.25) after regime check. 📉
2025-08-13 13:48:19,557 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,557 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,557 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'simple_put_signal' in 3min. 🗑️       
2025-08-13 13:48:19,557 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'long_call_otm' for 3min timeframe... 🔍
2025-08-13 13:48:19,558 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for long_call_otm in 3min...
2025-08-13 13:48:19,558 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for long_call_otm - generating simple signals 
2025-08-13 13:48:19,558 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for long_call_otm in 3min
2025-08-13 13:48:19,558 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 3min timeframe... 🤖
2025-08-13 13:48:19,559 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,559 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for long_call_otm in 3min... ⭐
2025-08-13 13:48:19,559 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'long_call_otm' (3min)... 📊 
2025-08-13 13:48:19,559 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,561 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,561 - agents.options_signal_generation_agent - INFO - [FILTER] Signal long_call_otm filtered out due to low confidence (0.42) after regime 
check. 📉
2025-08-13 13:48:19,561 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,561 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,561 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'long_call_otm' in 3min. 🗑️
2025-08-13 13:48:19,562 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'long_put_otm' for 3min timeframe... 🔍
2025-08-13 13:48:19,562 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for long_put_otm in 3min...
2025-08-13 13:48:19,562 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for long_put_otm - generating simple signals  
2025-08-13 13:48:19,562 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for long_put_otm in 3min
2025-08-13 13:48:19,562 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 3min timeframe... 🤖
2025-08-13 13:48:19,563 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,563 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for long_put_otm in 3min... ⭐
2025-08-13 13:48:19,563 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'long_put_otm' (3min)... 📊  
2025-08-13 13:48:19,563 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,564 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,565 - agents.options_signal_generation_agent - INFO - [FILTER] Signal long_put_otm filtered out due to low confidence (0.12) after regime check. 📉
2025-08-13 13:48:19,565 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,565 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,565 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'long_put_otm' in 3min. 🗑️
2025-08-13 13:48:19,565 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'long_straddle' for 3min timeframe... 🔍
2025-08-13 13:48:19,566 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for long_straddle in 3min...
2025-08-13 13:48:19,566 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for long_straddle - generating simple signals 
2025-08-13 13:48:19,566 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for long_straddle in 3min
2025-08-13 13:48:19,566 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 3min timeframe... 🤖
2025-08-13 13:48:19,566 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,567 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for long_straddle in 3min... ⭐
2025-08-13 13:48:19,567 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'long_straddle' (3min)... 📊 
2025-08-13 13:48:19,567 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,568 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,568 - agents.options_signal_generation_agent - INFO - [FILTER] Signal long_straddle filtered out due to low confidence (0.42) after regime 
check. 📉
2025-08-13 13:48:19,569 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,569 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,569 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'long_straddle' in 3min. 🗑️
2025-08-13 13:48:19,569 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'long_strangle' for 3min timeframe... 🔍
2025-08-13 13:48:19,569 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for long_strangle in 3min...
2025-08-13 13:48:19,569 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for long_strangle - generating simple signals 
2025-08-13 13:48:19,570 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for long_strangle in 3min
2025-08-13 13:48:19,570 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 3min timeframe... 🤖
2025-08-13 13:48:19,570 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,570 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for long_strangle in 3min... ⭐
2025-08-13 13:48:19,571 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'long_strangle' (3min)... 📊 
2025-08-13 13:48:19,571 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,572 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,572 - agents.options_signal_generation_agent - INFO - [FILTER] Signal long_strangle filtered out due to low confidence (0.42) after regime 
check. 📉
2025-08-13 13:48:19,573 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,573 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,573 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'long_strangle' in 3min. 🗑️
2025-08-13 13:48:19,573 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'bull_call_spread' for 3min timeframe... 🔍
2025-08-13 13:48:19,573 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for bull_call_spread in 3min...
2025-08-13 13:48:19,573 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for bull_call_spread - generating simple signals
2025-08-13 13:48:19,574 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for bull_call_spread in 3min
2025-08-13 13:48:19,574 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 3min timeframe... 🤖
2025-08-13 13:48:19,574 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,574 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for bull_call_spread in 3min... ⭐
2025-08-13 13:48:19,574 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'bull_call_spread' (3min)... 
📊
2025-08-13 13:48:19,575 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,576 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,576 - agents.options_signal_generation_agent - INFO - [FILTER] Signal bull_call_spread filtered out due to low confidence (0.42) after regime check. 📉
2025-08-13 13:48:19,576 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,577 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,577 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'bull_call_spread' in 3min. 🗑️        
2025-08-13 13:48:19,577 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'bear_put_spread' for 3min timeframe... 🔍
2025-08-13 13:48:19,577 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for bear_put_spread in 3min...
2025-08-13 13:48:19,577 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for bear_put_spread - generating simple signals
2025-08-13 13:48:19,578 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for bear_put_spread in 3min
2025-08-13 13:48:19,578 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 3min timeframe... 🤖
2025-08-13 13:48:19,578 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,578 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for bear_put_spread in 3min... ⭐
2025-08-13 13:48:19,579 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'bear_put_spread' (3min)... ��
2025-08-13 13:48:19,579 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,580 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,580 - agents.options_signal_generation_agent - INFO - [FILTER] Signal bear_put_spread filtered out due to low confidence (0.42) after regime check. 📉
2025-08-13 13:48:19,581 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,581 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,581 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'bear_put_spread' in 3min. 🗑️
2025-08-13 13:48:19,581 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'iron_condor' for 3min timeframe... 🔍
2025-08-13 13:48:19,581 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for iron_condor in 3min...
2025-08-13 13:48:19,581 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for iron_condor - generating simple signals   
2025-08-13 13:48:19,582 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for iron_condor in 3min
2025-08-13 13:48:19,582 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 3min timeframe... 🤖
2025-08-13 13:48:19,582 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,582 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for iron_condor in 3min... ⭐
2025-08-13 13:48:19,582 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'iron_condor' (3min)... 📊   
2025-08-13 13:48:19,583 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,584 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,584 - agents.options_signal_generation_agent - INFO - [FILTER] Signal iron_condor filtered out due to low confidence (0.42) after regime check. 📉
2025-08-13 13:48:19,584 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,584 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,585 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'iron_condor' in 3min. 🗑️
2025-08-13 13:48:19,585 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'short_straddle' for 3min timeframe... 🔍
2025-08-13 13:48:19,585 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for short_straddle in 3min...
2025-08-13 13:48:19,585 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for short_straddle - generating simple signals2025-08-13 13:48:19,585 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for short_straddle in 3min
2025-08-13 13:48:19,585 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 3min timeframe... 🤖
2025-08-13 13:48:19,586 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,586 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for short_straddle in 3min... ⭐
2025-08-13 13:48:19,586 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'short_straddle' (3min)... 📊
2025-08-13 13:48:19,586 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,588 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,588 - agents.options_signal_generation_agent - INFO - [FILTER] Signal short_straddle filtered out due to low confidence (0.42) after regime check. 📉
2025-08-13 13:48:19,588 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,588 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,588 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'short_straddle' in 3min. 🗑️
2025-08-13 13:48:19,592 - agents.options_signal_generation_agent - INFO - [REGIME] Fetching current market regime information... 🌍
2025-08-13 13:48:19,593 - agents.options_signal_generation_agent - INFO - [STRATEGY] Starting 20 strategy tasks for 5min timeframe...
2025-08-13 13:48:19,593 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'volatility_breakout_ce' for 5min timeframe... 🔍     
2025-08-13 13:48:19,593 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for volatility_breakout_ce in 5min...
2025-08-13 13:48:19,595 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for volatility_breakout_ce in 5min     
2025-08-13 13:48:19,595 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'volatility_breakout_ce' in 5min. 🤷       
2025-08-13 13:48:19,596 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'volatility_breakout_pe' for 5min timeframe... 🔍     
2025-08-13 13:48:19,596 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for volatility_breakout_pe in 5min...
2025-08-13 13:48:19,597 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for volatility_breakout_pe in 5min     
2025-08-13 13:48:19,597 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'volatility_breakout_pe' in 5min. 🤷       
2025-08-13 13:48:19,598 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'momentum_long_call' for 5min timeframe... 🔍
2025-08-13 13:48:19,598 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for momentum_long_call in 5min...
2025-08-13 13:48:19,599 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for momentum_long_call in 5min
2025-08-13 13:48:19,599 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'momentum_long_call' in 5min. 🤷
2025-08-13 13:48:19,600 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'momentum_long_put' for 5min timeframe... 🔍
2025-08-13 13:48:19,600 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for momentum_long_put in 5min...
2025-08-13 13:48:19,601 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for momentum_long_put in 5min
2025-08-13 13:48:19,601 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'momentum_long_put' in 5min. 🤷
2025-08-13 13:48:19,601 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'oversold_bounce' for 5min timeframe... 🔍
2025-08-13 13:48:19,602 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for oversold_bounce in 5min...
2025-08-13 13:48:19,603 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for oversold_bounce in 5min
2025-08-13 13:48:19,603 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'oversold_bounce' in 5min. 🤷
2025-08-13 13:48:19,603 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'overbought_fade' for 5min timeframe... 🔍
2025-08-13 13:48:19,604 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for overbought_fade in 5min...
2025-08-13 13:48:19,605 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for overbought_fade in 5min
2025-08-13 13:48:19,605 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'overbought_fade' in 5min. 🤷
2025-08-13 13:48:19,605 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'unusual_volume_ce' for 5min timeframe... 🔍
2025-08-13 13:48:19,606 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for unusual_volume_ce in 5min...
2025-08-13 13:48:19,607 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for unusual_volume_ce in 5min
2025-08-13 13:48:19,607 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'unusual_volume_ce' in 5min. 🤷
2025-08-13 13:48:19,607 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'unusual_volume_pe' for 5min timeframe... 🔍
2025-08-13 13:48:19,607 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for unusual_volume_pe in 5min...
2025-08-13 13:48:19,609 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for unusual_volume_pe in 5min
2025-08-13 13:48:19,609 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'unusual_volume_pe' in 5min. 🤷
2025-08-13 13:48:19,609 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'trend_following_ce' for 5min timeframe... 🔍
2025-08-13 13:48:19,609 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for trend_following_ce in 5min...
2025-08-13 13:48:19,611 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 signals for trend_following_ce in 5min
2025-08-13 13:48:19,611 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 5min timeframe... 🤖
2025-08-13 13:48:19,612 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,612 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for trend_following_ce in 5min... ⭐       
2025-08-13 13:48:19,612 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'trend_following_ce' (5min)... 📊
2025-08-13 13:48:19,612 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,613 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,614 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,614 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,614 - agents.options_signal_generation_agent - INFO - [SIGNAL] Generated 1 final signals for strategy 'trend_following_ce' in 5min 🎉      
2025-08-13 13:48:19,614 - agents.options_signal_generation_agent - INFO - [LLM] Generating natural language signal summary... 📝
2025-08-13 13:48:19,614 - agents.options_signal_generation_agent - INFO - [SUMMARY] Example Signal Summary: BANKNIFTY 0 CE is a high-confidence BUY CE signal due to various technical factors. Strategy 'trend_following_ce' is active. 🗣️
2025-08-13 13:48:19,615 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'trend_following_pe' for 5min timeframe... 🔍
2025-08-13 13:48:19,615 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for trend_following_pe in 5min...
2025-08-13 13:48:19,616 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for trend_following_pe in 5min
2025-08-13 13:48:19,616 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'trend_following_pe' in 5min. 🤷
2025-08-13 13:48:19,616 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'simple_call_signal' for 5min timeframe... 🔍
2025-08-13 13:48:19,617 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for simple_call_signal in 5min...
2025-08-13 13:48:19,617 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for simple_call_signal - generating simple signals
2025-08-13 13:48:19,617 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for simple_call_signal in 5min
2025-08-13 13:48:19,617 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 5min timeframe... 🤖
2025-08-13 13:48:19,618 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,618 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for simple_call_signal in 5min... ⭐       
2025-08-13 13:48:19,618 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'simple_call_signal' (5min)... 📊
2025-08-13 13:48:19,618 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,619 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,620 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,620 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,620 - agents.options_signal_generation_agent - INFO - [SIGNAL] Generated 1 final signals for strategy 'simple_call_signal' in 5min 🎉      
2025-08-13 13:48:19,620 - agents.options_signal_generation_agent - INFO - [LLM] Generating natural language signal summary... 📝
2025-08-13 13:48:19,620 - agents.options_signal_generation_agent - INFO - [SUMMARY] Example Signal Summary: BANKNIFTY 0 CE is a high-confidence BUY CE signal due to various technical factors. Strategy 'simple_call_signal' is active. 🗣️
2025-08-13 13:48:19,621 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'simple_put_signal' for 5min timeframe... 🔍
2025-08-13 13:48:19,621 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for simple_put_signal in 5min...
2025-08-13 13:48:19,621 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for simple_put_signal - generating simple signals
2025-08-13 13:48:19,621 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for simple_put_signal in 5min
2025-08-13 13:48:19,621 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 5min timeframe... 🤖
2025-08-13 13:48:19,622 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,622 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for simple_put_signal in 5min... ⭐        
2025-08-13 13:48:19,622 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'simple_put_signal' (5min)... 📊
2025-08-13 13:48:19,622 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,623 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,624 - agents.options_signal_generation_agent - INFO - [FILTER] Signal simple_put_signal filtered out due to low confidence (0.25) after regime check. 📉
2025-08-13 13:48:19,624 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,624 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,624 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'simple_put_signal' in 5min. 🗑️       
2025-08-13 13:48:19,624 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'long_call_otm' for 5min timeframe... 🔍
2025-08-13 13:48:19,625 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for long_call_otm in 5min...
2025-08-13 13:48:19,625 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for long_call_otm - generating simple signals 
2025-08-13 13:48:19,625 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for long_call_otm in 5min
2025-08-13 13:48:19,625 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 5min timeframe... 🤖
2025-08-13 13:48:19,625 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,626 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for long_call_otm in 5min... ⭐
2025-08-13 13:48:19,626 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'long_call_otm' (5min)... 📊 
2025-08-13 13:48:19,626 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,627 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,627 - agents.options_signal_generation_agent - INFO - [FILTER] Signal long_call_otm filtered out due to low confidence (0.42) after regime 
check. 📉
2025-08-13 13:48:19,628 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,628 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,628 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'long_call_otm' in 5min. 🗑️
2025-08-13 13:48:19,628 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'long_put_otm' for 5min timeframe... 🔍
2025-08-13 13:48:19,628 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for long_put_otm in 5min...
2025-08-13 13:48:19,628 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for long_put_otm - generating simple signals  
2025-08-13 13:48:19,629 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for long_put_otm in 5min
2025-08-13 13:48:19,629 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 5min timeframe... 🤖
2025-08-13 13:48:19,629 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,629 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for long_put_otm in 5min... ⭐
2025-08-13 13:48:19,629 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'long_put_otm' (5min)... 📊  
2025-08-13 13:48:19,630 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,631 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,631 - agents.options_signal_generation_agent - INFO - [FILTER] Signal long_put_otm filtered out due to low confidence (0.12) after regime check. 📉
2025-08-13 13:48:19,631 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,631 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,632 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'long_put_otm' in 5min. 🗑️
2025-08-13 13:48:19,632 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'long_straddle' for 5min timeframe... 🔍
2025-08-13 13:48:19,632 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for long_straddle in 5min...
2025-08-13 13:48:19,632 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for long_straddle - generating simple signals 
2025-08-13 13:48:19,632 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for long_straddle in 5min
2025-08-13 13:48:19,633 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 5min timeframe... 🤖
2025-08-13 13:48:19,633 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,633 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for long_straddle in 5min... ⭐
2025-08-13 13:48:19,633 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'long_straddle' (5min)... 📊 
2025-08-13 13:48:19,633 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,635 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,635 - agents.options_signal_generation_agent - INFO - [FILTER] Signal long_straddle filtered out due to low confidence (0.42) after regime 
check. 📉
2025-08-13 13:48:19,635 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,635 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,635 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'long_straddle' in 5min. 🗑️
2025-08-13 13:48:19,635 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'long_strangle' for 5min timeframe... 🔍
2025-08-13 13:48:19,636 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for long_strangle in 5min...
2025-08-13 13:48:19,636 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for long_strangle - generating simple signals 
2025-08-13 13:48:19,636 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for long_strangle in 5min
2025-08-13 13:48:19,636 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 5min timeframe... 🤖
2025-08-13 13:48:19,637 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,637 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for long_strangle in 5min... ⭐
2025-08-13 13:48:19,637 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'long_strangle' (5min)... 📊 
2025-08-13 13:48:19,637 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,638 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,639 - agents.options_signal_generation_agent - INFO - [FILTER] Signal long_strangle filtered out due to low confidence (0.42) after regime 
check. 📉
2025-08-13 13:48:19,639 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,639 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,639 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'long_strangle' in 5min. 🗑️
2025-08-13 13:48:19,639 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'bull_call_spread' for 5min timeframe... 🔍
2025-08-13 13:48:19,639 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for bull_call_spread in 5min...
2025-08-13 13:48:19,640 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for bull_call_spread - generating simple signals
2025-08-13 13:48:19,640 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for bull_call_spread in 5min
2025-08-13 13:48:19,640 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 5min timeframe... 🤖
2025-08-13 13:48:19,641 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,641 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for bull_call_spread in 5min... ⭐
2025-08-13 13:48:19,641 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'bull_call_spread' (5min)... 
📊
2025-08-13 13:48:19,641 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,643 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,643 - agents.options_signal_generation_agent - INFO - [FILTER] Signal bull_call_spread filtered out due to low confidence (0.42) after regime check. 📉
2025-08-13 13:48:19,643 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,643 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,644 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'bull_call_spread' in 5min. 🗑️        
2025-08-13 13:48:19,644 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'bear_put_spread' for 5min timeframe... 🔍
2025-08-13 13:48:19,644 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for bear_put_spread in 5min...
2025-08-13 13:48:19,644 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for bear_put_spread - generating simple signals
2025-08-13 13:48:19,645 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for bear_put_spread in 5min
2025-08-13 13:48:19,645 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 5min timeframe... 🤖
2025-08-13 13:48:19,645 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,645 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for bear_put_spread in 5min... ⭐
2025-08-13 13:48:19,646 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'bear_put_spread' (5min)... ��
2025-08-13 13:48:19,646 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,647 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,647 - agents.options_signal_generation_agent - INFO - [FILTER] Signal bear_put_spread filtered out due to low confidence (0.42) after regime check. 📉
2025-08-13 13:48:19,648 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,648 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,648 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'bear_put_spread' in 5min. 🗑️
2025-08-13 13:48:19,648 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'iron_condor' for 5min timeframe... 🔍
2025-08-13 13:48:19,648 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for iron_condor in 5min...
2025-08-13 13:48:19,648 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for iron_condor - generating simple signals   
2025-08-13 13:48:19,649 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for iron_condor in 5min
2025-08-13 13:48:19,649 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 5min timeframe... 🤖
2025-08-13 13:48:19,649 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,649 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for iron_condor in 5min... ⭐
2025-08-13 13:48:19,650 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'iron_condor' (5min)... 📊   
2025-08-13 13:48:19,650 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,651 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,651 - agents.options_signal_generation_agent - INFO - [FILTER] Signal iron_condor filtered out due to low confidence (0.42) after regime check. 📉
2025-08-13 13:48:19,651 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,652 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,652 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'iron_condor' in 5min. 🗑️
2025-08-13 13:48:19,652 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'short_straddle' for 5min timeframe... 🔍
2025-08-13 13:48:19,652 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for short_straddle in 5min...
2025-08-13 13:48:19,652 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for short_straddle - generating simple signals2025-08-13 13:48:19,653 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for short_straddle in 5min
2025-08-13 13:48:19,653 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 5min timeframe... 🤖
2025-08-13 13:48:19,653 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,653 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for short_straddle in 5min... ⭐
2025-08-13 13:48:19,653 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'short_straddle' (5min)... 📊
2025-08-13 13:48:19,653 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,655 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,655 - agents.options_signal_generation_agent - INFO - [FILTER] Signal short_straddle filtered out due to low confidence (0.42) after regime check. 📉
2025-08-13 13:48:19,655 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,655 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,656 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'short_straddle' in 5min. 🗑️
2025-08-13 13:48:19,656 - agents.options_signal_generation_agent - INFO - [STRATEGY] Completed 20 strategy tasks for 3min timeframe
2025-08-13 13:48:19,656 - agents.options_signal_generation_agent - INFO - [STRATEGY] Completed 20 strategy tasks for 5min timeframe
2025-08-13 13:48:19,663 - agents.options_signal_generation_agent - INFO - [REGIME] Fetching current market regime information... 🌍
2025-08-13 13:48:19,663 - agents.options_signal_generation_agent - INFO - [STRATEGY] Starting 20 strategy tasks for 15min timeframe...
2025-08-13 13:48:19,664 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'volatility_breakout_ce' for 15min timeframe... 🔍    
2025-08-13 13:48:19,664 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for volatility_breakout_ce in 15min...
2025-08-13 13:48:19,666 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for volatility_breakout_ce in 15min
2025-08-13 13:48:19,666 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'volatility_breakout_ce' in 15min. 🤷      
2025-08-13 13:48:19,666 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'volatility_breakout_pe' for 15min timeframe... 🔍    
2025-08-13 13:48:19,666 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for volatility_breakout_pe in 15min...
2025-08-13 13:48:19,668 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for volatility_breakout_pe in 15min    
2025-08-13 13:48:19,668 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'volatility_breakout_pe' in 15min. 🤷      
2025-08-13 13:48:19,668 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'momentum_long_call' for 15min timeframe... 🔍        
2025-08-13 13:48:19,668 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for momentum_long_call in 15min...
2025-08-13 13:48:19,670 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for momentum_long_call in 15min        
2025-08-13 13:48:19,670 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'momentum_long_call' in 15min. 🤷
2025-08-13 13:48:19,670 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'momentum_long_put' for 15min timeframe... 🔍
2025-08-13 13:48:19,670 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for momentum_long_put in 15min...
2025-08-13 13:48:19,672 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for momentum_long_put in 15min
2025-08-13 13:48:19,672 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'momentum_long_put' in 15min. 🤷
2025-08-13 13:48:19,672 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'oversold_bounce' for 15min timeframe... 🔍
2025-08-13 13:48:19,672 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for oversold_bounce in 15min...
2025-08-13 13:48:19,674 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for oversold_bounce in 15min
2025-08-13 13:48:19,674 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'oversold_bounce' in 15min. 🤷
2025-08-13 13:48:19,674 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'overbought_fade' for 15min timeframe... 🔍
2025-08-13 13:48:19,674 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for overbought_fade in 15min...
2025-08-13 13:48:19,676 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for overbought_fade in 15min
2025-08-13 13:48:19,676 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'overbought_fade' in 15min. 🤷
2025-08-13 13:48:19,676 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'unusual_volume_ce' for 15min timeframe... 🔍
2025-08-13 13:48:19,677 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for unusual_volume_ce in 15min...
2025-08-13 13:48:19,678 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for unusual_volume_ce in 15min
2025-08-13 13:48:19,678 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'unusual_volume_ce' in 15min. 🤷
2025-08-13 13:48:19,679 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'unusual_volume_pe' for 15min timeframe... 🔍
2025-08-13 13:48:19,679 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for unusual_volume_pe in 15min...
2025-08-13 13:48:19,680 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for unusual_volume_pe in 15min
2025-08-13 13:48:19,680 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'unusual_volume_pe' in 15min. 🤷
2025-08-13 13:48:19,681 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'trend_following_ce' for 15min timeframe... 🔍        
2025-08-13 13:48:19,681 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for trend_following_ce in 15min...
2025-08-13 13:48:19,682 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 signals for trend_following_ce in 15min
2025-08-13 13:48:19,683 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 15min timeframe... 🤖
2025-08-13 13:48:19,683 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,683 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for trend_following_ce in 15min... ⭐      
2025-08-13 13:48:19,683 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'trend_following_ce' (15min)... 📊
2025-08-13 13:48:19,683 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,685 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,685 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,685 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,685 - agents.options_signal_generation_agent - INFO - [SIGNAL] Generated 1 final signals for strategy 'trend_following_ce' in 15min 🎉     
2025-08-13 13:48:19,686 - agents.options_signal_generation_agent - INFO - [LLM] Generating natural language signal summary... 📝
2025-08-13 13:48:19,686 - agents.options_signal_generation_agent - INFO - [SUMMARY] Example Signal Summary: BANKNIFTY 0 CE is a high-confidence BUY CE signal due to various technical factors. Strategy 'trend_following_ce' is active. 🗣️
2025-08-13 13:48:19,686 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'trend_following_pe' for 15min timeframe... 🔍        
2025-08-13 13:48:19,686 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for trend_following_pe in 15min...
2025-08-13 13:48:19,688 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for trend_following_pe in 15min        
2025-08-13 13:48:19,688 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'trend_following_pe' in 15min. 🤷
2025-08-13 13:48:19,688 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'simple_call_signal' for 15min timeframe... 🔍        
2025-08-13 13:48:19,688 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for simple_call_signal in 15min...
2025-08-13 13:48:19,688 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for simple_call_signal - generating simple signals
2025-08-13 13:48:19,689 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for simple_call_signal in 15min
2025-08-13 13:48:19,689 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 15min timeframe... 🤖
2025-08-13 13:48:19,689 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,689 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for simple_call_signal in 15min... ⭐      
2025-08-13 13:48:19,689 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'simple_call_signal' (15min)... 📊
2025-08-13 13:48:19,690 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,691 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,691 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,691 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,691 - agents.options_signal_generation_agent - INFO - [SIGNAL] Generated 1 final signals for strategy 'simple_call_signal' in 15min 🎉     
2025-08-13 13:48:19,692 - agents.options_signal_generation_agent - INFO - [LLM] Generating natural language signal summary... 📝
2025-08-13 13:48:19,692 - agents.options_signal_generation_agent - INFO - [SUMMARY] Example Signal Summary: BANKNIFTY 0 CE is a high-confidence BUY CE signal due to various technical factors. Strategy 'simple_call_signal' is active. 🗣️
2025-08-13 13:48:19,692 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'simple_put_signal' for 15min timeframe... 🔍
2025-08-13 13:48:19,692 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for simple_put_signal in 15min...
2025-08-13 13:48:19,692 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for simple_put_signal - generating simple signals
2025-08-13 13:48:19,693 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for simple_put_signal in 15min
2025-08-13 13:48:19,693 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 15min timeframe... 🤖
2025-08-13 13:48:19,693 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,693 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for simple_put_signal in 15min... ⭐       
2025-08-13 13:48:19,694 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'simple_put_signal' (15min)... 📊
2025-08-13 13:48:19,694 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,695 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,695 - agents.options_signal_generation_agent - INFO - [FILTER] Signal simple_put_signal filtered out due to low confidence (0.25) after regime check. 📉
2025-08-13 13:48:19,696 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,696 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,696 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'simple_put_signal' in 15min. 🗑️      
2025-08-13 13:48:19,696 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'long_call_otm' for 15min timeframe... 🔍
2025-08-13 13:48:19,696 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for long_call_otm in 15min...
2025-08-13 13:48:19,696 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for long_call_otm - generating simple signals 
2025-08-13 13:48:19,697 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for long_call_otm in 15min
2025-08-13 13:48:19,697 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 15min timeframe... 🤖
2025-08-13 13:48:19,697 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,697 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for long_call_otm in 15min... ⭐
2025-08-13 13:48:19,697 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'long_call_otm' (15min)... 📊
2025-08-13 13:48:19,698 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,699 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,699 - agents.options_signal_generation_agent - INFO - [FILTER] Signal long_call_otm filtered out due to low confidence (0.42) after regime 
check. 📉
2025-08-13 13:48:19,699 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,699 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,700 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'long_call_otm' in 15min. 🗑️
2025-08-13 13:48:19,700 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'long_put_otm' for 15min timeframe... 🔍
2025-08-13 13:48:19,700 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for long_put_otm in 15min...
2025-08-13 13:48:19,700 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for long_put_otm - generating simple signals  
2025-08-13 13:48:19,700 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for long_put_otm in 15min
2025-08-13 13:48:19,701 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 15min timeframe... 🤖
2025-08-13 13:48:19,701 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,701 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for long_put_otm in 15min... ⭐
2025-08-13 13:48:19,701 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'long_put_otm' (15min)... 📊 
2025-08-13 13:48:19,701 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,703 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,703 - agents.options_signal_generation_agent - INFO - [FILTER] Signal long_put_otm filtered out due to low confidence (0.12) after regime check. 📉
2025-08-13 13:48:19,703 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,703 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,703 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'long_put_otm' in 15min. 🗑️
2025-08-13 13:48:19,703 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'long_straddle' for 15min timeframe... 🔍
2025-08-13 13:48:19,704 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for long_straddle in 15min...
2025-08-13 13:48:19,704 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for long_straddle - generating simple signals 
2025-08-13 13:48:19,704 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for long_straddle in 15min
2025-08-13 13:48:19,704 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 15min timeframe... 🤖
2025-08-13 13:48:19,705 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,705 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for long_straddle in 15min... ⭐
2025-08-13 13:48:19,705 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'long_straddle' (15min)... 📊
2025-08-13 13:48:19,705 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,706 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,706 - agents.options_signal_generation_agent - INFO - [FILTER] Signal long_straddle filtered out due to low confidence (0.42) after regime 
check. 📉
2025-08-13 13:48:19,707 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,707 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,707 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'long_straddle' in 15min. 🗑️
2025-08-13 13:48:19,707 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'long_strangle' for 15min timeframe... 🔍
2025-08-13 13:48:19,707 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for long_strangle in 15min...
2025-08-13 13:48:19,707 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for long_strangle - generating simple signals 
2025-08-13 13:48:19,708 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for long_strangle in 15min
2025-08-13 13:48:19,708 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 15min timeframe... 🤖
2025-08-13 13:48:19,708 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,708 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for long_strangle in 15min... ⭐
2025-08-13 13:48:19,709 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'long_strangle' (15min)... 📊
2025-08-13 13:48:19,709 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,710 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,710 - agents.options_signal_generation_agent - INFO - [FILTER] Signal long_strangle filtered out due to low confidence (0.42) after regime 
check. 📉
2025-08-13 13:48:19,710 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,711 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,711 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'long_strangle' in 15min. 🗑️
2025-08-13 13:48:19,711 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'bull_call_spread' for 15min timeframe... 🔍
2025-08-13 13:48:19,711 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for bull_call_spread in 15min...
2025-08-13 13:48:19,711 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for bull_call_spread - generating simple signals
2025-08-13 13:48:19,712 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for bull_call_spread in 15min
2025-08-13 13:48:19,712 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 15min timeframe... 🤖
2025-08-13 13:48:19,712 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,712 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for bull_call_spread in 15min... ⭐        
2025-08-13 13:48:19,712 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'bull_call_spread' (15min)... 📊
2025-08-13 13:48:19,713 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,714 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,714 - agents.options_signal_generation_agent - INFO - [FILTER] Signal bull_call_spread filtered out due to low confidence (0.42) after regime check. 📉
2025-08-13 13:48:19,714 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,715 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,715 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'bull_call_spread' in 15min. 🗑️       
2025-08-13 13:48:19,715 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'bear_put_spread' for 15min timeframe... 🔍
2025-08-13 13:48:19,715 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for bear_put_spread in 15min...
2025-08-13 13:48:19,715 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for bear_put_spread - generating simple signals
2025-08-13 13:48:19,716 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for bear_put_spread in 15min
2025-08-13 13:48:19,716 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 15min timeframe... 🤖
2025-08-13 13:48:19,716 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,716 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for bear_put_spread in 15min... ⭐
2025-08-13 13:48:19,716 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'bear_put_spread' (15min)... 
📊
2025-08-13 13:48:19,717 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,718 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,718 - agents.options_signal_generation_agent - INFO - [FILTER] Signal bear_put_spread filtered out due to low confidence (0.42) after regime check. 📉
2025-08-13 13:48:19,718 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,718 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,719 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'bear_put_spread' in 15min. 🗑️        
2025-08-13 13:48:19,719 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'iron_condor' for 15min timeframe... 🔍
2025-08-13 13:48:19,719 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for iron_condor in 15min...
2025-08-13 13:48:19,719 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for iron_condor - generating simple signals   
2025-08-13 13:48:19,719 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for iron_condor in 15min
2025-08-13 13:48:19,720 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 15min timeframe... 🤖
2025-08-13 13:48:19,720 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,720 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for iron_condor in 15min... ⭐
2025-08-13 13:48:19,720 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'iron_condor' (15min)... 📊  
2025-08-13 13:48:19,720 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,721 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,722 - agents.options_signal_generation_agent - INFO - [FILTER] Signal iron_condor filtered out due to low confidence (0.42) after regime check. 📉
2025-08-13 13:48:19,722 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,722 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,722 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'iron_condor' in 15min. 🗑️
2025-08-13 13:48:19,722 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'short_straddle' for 15min timeframe... 🔍
2025-08-13 13:48:19,723 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for short_straddle in 15min...
2025-08-13 13:48:19,723 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for short_straddle - generating simple signals2025-08-13 13:48:19,723 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for short_straddle in 15min
2025-08-13 13:48:19,723 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 15min timeframe... 🤖
2025-08-13 13:48:19,723 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,724 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for short_straddle in 15min... ⭐
2025-08-13 13:48:19,724 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'short_straddle' (15min)... ��
2025-08-13 13:48:19,724 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,725 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,725 - agents.options_signal_generation_agent - INFO - [FILTER] Signal short_straddle filtered out due to low confidence (0.42) after regime check. 📉
2025-08-13 13:48:19,726 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,726 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,726 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'short_straddle' in 15min. 🗑️
2025-08-13 13:48:19,726 - agents.options_signal_generation_agent - INFO - [STRATEGY] Completed 20 strategy tasks for 15min timeframe
2025-08-13 13:48:19,732 - agents.options_signal_generation_agent - INFO - [REGIME] Fetching current market regime information... 🌍
2025-08-13 13:48:19,733 - agents.options_signal_generation_agent - INFO - [STRATEGY] Starting 20 strategy tasks for 1min timeframe...
2025-08-13 13:48:19,733 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'volatility_breakout_ce' for 1min timeframe... 🔍     
2025-08-13 13:48:19,733 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for volatility_breakout_ce in 1min...
2025-08-13 13:48:19,735 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for volatility_breakout_ce in 1min     
2025-08-13 13:48:19,735 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'volatility_breakout_ce' in 1min. 🤷       
2025-08-13 13:48:19,735 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'volatility_breakout_pe' for 1min timeframe... 🔍     
2025-08-13 13:48:19,735 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for volatility_breakout_pe in 1min...
2025-08-13 13:48:19,737 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for volatility_breakout_pe in 1min     
2025-08-13 13:48:19,737 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'volatility_breakout_pe' in 1min. 🤷       
2025-08-13 13:48:19,737 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'momentum_long_call' for 1min timeframe... 🔍
2025-08-13 13:48:19,737 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for momentum_long_call in 1min...
2025-08-13 13:48:19,739 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for momentum_long_call in 1min
2025-08-13 13:48:19,739 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'momentum_long_call' in 1min. 🤷
2025-08-13 13:48:19,739 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'momentum_long_put' for 1min timeframe... 🔍
2025-08-13 13:48:19,739 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for momentum_long_put in 1min...
2025-08-13 13:48:19,741 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for momentum_long_put in 1min
2025-08-13 13:48:19,741 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'momentum_long_put' in 1min. 🤷
2025-08-13 13:48:19,741 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'oversold_bounce' for 1min timeframe... 🔍
2025-08-13 13:48:19,741 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for oversold_bounce in 1min...
2025-08-13 13:48:19,743 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for oversold_bounce in 1min
2025-08-13 13:48:19,743 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'oversold_bounce' in 1min. 🤷
2025-08-13 13:48:19,743 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'overbought_fade' for 1min timeframe... 🔍
2025-08-13 13:48:19,743 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for overbought_fade in 1min...
2025-08-13 13:48:19,745 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for overbought_fade in 1min
2025-08-13 13:48:19,745 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'overbought_fade' in 1min. 🤷
2025-08-13 13:48:19,745 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'unusual_volume_ce' for 1min timeframe... 🔍
2025-08-13 13:48:19,746 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for unusual_volume_ce in 1min...
2025-08-13 13:48:19,747 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for unusual_volume_ce in 1min
2025-08-13 13:48:19,747 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'unusual_volume_ce' in 1min. 🤷
2025-08-13 13:48:19,747 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'unusual_volume_pe' for 1min timeframe... 🔍
2025-08-13 13:48:19,748 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for unusual_volume_pe in 1min...
2025-08-13 13:48:19,749 - agents.options_signal_generation_agent - INFO - [STRATEGY] No data points meet all conditions for unusual_volume_pe in 1min
2025-08-13 13:48:19,749 - agents.options_signal_generation_agent - INFO - [SIGNAL] No signals generated by strategy 'unusual_volume_pe' in 1min. 🤷
2025-08-13 13:48:19,749 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'trend_following_ce' for 1min timeframe... 🔍
2025-08-13 13:48:19,750 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for trend_following_ce in 1min...
2025-08-13 13:48:19,751 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 signals for trend_following_ce in 1min
2025-08-13 13:48:19,751 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 1min timeframe... 🤖
2025-08-13 13:48:19,752 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,752 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for trend_following_ce in 1min... ⭐       
2025-08-13 13:48:19,752 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'trend_following_ce' (1min)... 📊
2025-08-13 13:48:19,752 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,753 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,754 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,754 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,754 - agents.options_signal_generation_agent - INFO - [SIGNAL] Generated 1 final signals for strategy 'trend_following_ce' in 1min 🎉      
2025-08-13 13:48:19,754 - agents.options_signal_generation_agent - INFO - [LLM] Generating natural language signal summary... 📝
2025-08-13 13:48:19,754 - agents.options_signal_generation_agent - INFO - [SUMMARY] Example Signal Summary: BANKNIFTY 0 CE is a high-confidence BUY CE signal due to various technical factors. Strategy 'trend_following_ce' is active. 🗣️
2025-08-13 13:48:19,755 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'trend_following_pe' for 1min timeframe... 🔍
2025-08-13 13:48:19,755 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for trend_following_pe in 1min...
2025-08-13 13:48:19,756 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 signals for trend_following_pe in 1min
2025-08-13 13:48:19,757 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 1min timeframe... 🤖
2025-08-13 13:48:19,757 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,757 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for trend_following_pe in 1min... ⭐       
2025-08-13 13:48:19,757 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'trend_following_pe' (1min)... 📊
2025-08-13 13:48:19,757 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,759 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,759 - agents.options_signal_generation_agent - INFO - [FILTER] Signal trend_following_pe filtered out due to low confidence (0.25) after regime check. 📉
2025-08-13 13:48:19,759 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,759 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,759 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'trend_following_pe' in 1min. 🗑️      
2025-08-13 13:48:19,760 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'simple_call_signal' for 1min timeframe... 🔍
2025-08-13 13:48:19,760 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for simple_call_signal in 1min...
2025-08-13 13:48:19,760 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for simple_call_signal - generating simple signals
2025-08-13 13:48:19,760 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for simple_call_signal in 1min
2025-08-13 13:48:19,760 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 1min timeframe... 🤖
2025-08-13 13:48:19,761 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,761 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for simple_call_signal in 1min... ⭐       
2025-08-13 13:48:19,761 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'simple_call_signal' (1min)... 📊
2025-08-13 13:48:19,761 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,763 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,763 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,763 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,763 - agents.options_signal_generation_agent - INFO - [SIGNAL] Generated 1 final signals for strategy 'simple_call_signal' in 1min 🎉      
2025-08-13 13:48:19,763 - agents.options_signal_generation_agent - INFO - [LLM] Generating natural language signal summary... 📝
2025-08-13 13:48:19,764 - agents.options_signal_generation_agent - INFO - [SUMMARY] Example Signal Summary: BANKNIFTY 0 CE is a high-confidence BUY CE signal due to various technical factors. Strategy 'simple_call_signal' is active. 🗣️
2025-08-13 13:48:19,764 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'simple_put_signal' for 1min timeframe... 🔍
2025-08-13 13:48:19,764 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for simple_put_signal in 1min...
2025-08-13 13:48:19,764 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for simple_put_signal - generating simple signals
2025-08-13 13:48:19,765 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for simple_put_signal in 1min
2025-08-13 13:48:19,765 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 1min timeframe... 🤖
2025-08-13 13:48:19,765 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,765 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for simple_put_signal in 1min... ⭐        
2025-08-13 13:48:19,765 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'simple_put_signal' (1min)... 📊
2025-08-13 13:48:19,765 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,767 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,767 - agents.options_signal_generation_agent - INFO - [FILTER] Signal simple_put_signal filtered out due to low confidence (0.25) after regime check. 📉
2025-08-13 13:48:19,767 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,767 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,767 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'simple_put_signal' in 1min. 🗑️       
2025-08-13 13:48:19,768 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'long_call_otm' for 1min timeframe... 🔍
2025-08-13 13:48:19,768 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for long_call_otm in 1min...
2025-08-13 13:48:19,768 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for long_call_otm - generating simple signals 
2025-08-13 13:48:19,768 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for long_call_otm in 1min
2025-08-13 13:48:19,768 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 1min timeframe... 🤖
2025-08-13 13:48:19,769 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,769 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for long_call_otm in 1min... ⭐
2025-08-13 13:48:19,769 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'long_call_otm' (1min)... 📊 
2025-08-13 13:48:19,769 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,770 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,771 - agents.options_signal_generation_agent - INFO - [FILTER] Signal long_call_otm filtered out due to low confidence (0.42) after regime 
check. 📉
2025-08-13 13:48:19,771 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,771 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,771 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'long_call_otm' in 1min. 🗑️
2025-08-13 13:48:19,771 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'long_put_otm' for 1min timeframe... 🔍
2025-08-13 13:48:19,771 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for long_put_otm in 1min...
2025-08-13 13:48:19,772 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for long_put_otm - generating simple signals  
2025-08-13 13:48:19,772 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for long_put_otm in 1min
2025-08-13 13:48:19,772 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 1min timeframe... 🤖
2025-08-13 13:48:19,772 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,773 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for long_put_otm in 1min... ⭐
2025-08-13 13:48:19,773 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'long_put_otm' (1min)... 📊  
2025-08-13 13:48:19,773 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,774 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,774 - agents.options_signal_generation_agent - INFO - [FILTER] Signal long_put_otm filtered out due to low confidence (0.12) after regime check. 📉
2025-08-13 13:48:19,774 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,775 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,775 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'long_put_otm' in 1min. 🗑️
2025-08-13 13:48:19,775 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'long_straddle' for 1min timeframe... 🔍
2025-08-13 13:48:19,775 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for long_straddle in 1min...
2025-08-13 13:48:19,775 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for long_straddle - generating simple signals 
2025-08-13 13:48:19,776 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for long_straddle in 1min
2025-08-13 13:48:19,776 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 1min timeframe... 🤖
2025-08-13 13:48:19,776 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,776 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for long_straddle in 1min... ⭐
2025-08-13 13:48:19,777 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'long_straddle' (1min)... 📊 
2025-08-13 13:48:19,777 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,778 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,778 - agents.options_signal_generation_agent - INFO - [FILTER] Signal long_straddle filtered out due to low confidence (0.42) after regime 
check. 📉
2025-08-13 13:48:19,778 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,779 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,779 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'long_straddle' in 1min. 🗑️
2025-08-13 13:48:19,779 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'long_strangle' for 1min timeframe... 🔍
2025-08-13 13:48:19,779 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for long_strangle in 1min...
2025-08-13 13:48:19,779 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for long_strangle - generating simple signals 
2025-08-13 13:48:19,780 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for long_strangle in 1min
2025-08-13 13:48:19,780 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 1min timeframe... 🤖
2025-08-13 13:48:19,780 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,780 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for long_strangle in 1min... ⭐
2025-08-13 13:48:19,780 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'long_strangle' (1min)... 📊 
2025-08-13 13:48:19,781 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,782 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,782 - agents.options_signal_generation_agent - INFO - [FILTER] Signal long_strangle filtered out due to low confidence (0.42) after regime 
check. 📉
2025-08-13 13:48:19,782 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,782 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,783 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'long_strangle' in 1min. 🗑️
2025-08-13 13:48:19,783 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'bull_call_spread' for 1min timeframe... 🔍
2025-08-13 13:48:19,783 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for bull_call_spread in 1min...
2025-08-13 13:48:19,783 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for bull_call_spread - generating simple signals
2025-08-13 13:48:19,783 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for bull_call_spread in 1min
2025-08-13 13:48:19,784 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 1min timeframe... 🤖
2025-08-13 13:48:19,784 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,784 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for bull_call_spread in 1min... ⭐
2025-08-13 13:48:19,784 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'bull_call_spread' (1min)... 
📊
2025-08-13 13:48:19,784 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,786 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,786 - agents.options_signal_generation_agent - INFO - [FILTER] Signal bull_call_spread filtered out due to low confidence (0.42) after regime check. 📉
2025-08-13 13:48:19,786 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,786 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,786 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'bull_call_spread' in 1min. 🗑️        
2025-08-13 13:48:19,786 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'bear_put_spread' for 1min timeframe... 🔍
2025-08-13 13:48:19,787 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for bear_put_spread in 1min...
2025-08-13 13:48:19,787 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for bear_put_spread - generating simple signals
2025-08-13 13:48:19,787 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for bear_put_spread in 1min
2025-08-13 13:48:19,787 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 1min timeframe... 🤖
2025-08-13 13:48:19,788 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,788 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for bear_put_spread in 1min... ⭐
2025-08-13 13:48:19,788 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'bear_put_spread' (1min)... ��
2025-08-13 13:48:19,788 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,789 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,790 - agents.options_signal_generation_agent - INFO - [FILTER] Signal bear_put_spread filtered out due to low confidence (0.42) after regime check. 📉
2025-08-13 13:48:19,790 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,790 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,790 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'bear_put_spread' in 1min. 🗑️
2025-08-13 13:48:19,790 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'iron_condor' for 1min timeframe... 🔍
2025-08-13 13:48:19,791 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for iron_condor in 1min...
2025-08-13 13:48:19,791 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for iron_condor - generating simple signals   
2025-08-13 13:48:19,791 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for iron_condor in 1min
2025-08-13 13:48:19,791 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 1min timeframe... 🤖
2025-08-13 13:48:19,791 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,792 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for iron_condor in 1min... ⭐
2025-08-13 13:48:19,792 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'iron_condor' (1min)... 📊   
2025-08-13 13:48:19,792 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,793 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,794 - agents.options_signal_generation_agent - INFO - [FILTER] Signal iron_condor filtered out due to low confidence (0.42) after regime check. 📉
2025-08-13 13:48:19,794 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,794 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,794 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'iron_condor' in 1min. 🗑️
2025-08-13 13:48:19,795 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating strategy 'short_straddle' for 1min timeframe... 🔍
2025-08-13 13:48:19,795 - agents.options_signal_generation_agent - INFO - [STRATEGY] Evaluating conditions for short_straddle in 1min...
2025-08-13 13:48:19,795 - agents.options_signal_generation_agent - INFO - [STRATEGY] No entry conditions defined for short_straddle - generating simple signals2025-08-13 13:48:19,795 - agents.options_signal_generation_agent - INFO - [STRATEGY] Generated 1 simple signals for short_straddle in 1min
2025-08-13 13:48:19,795 - agents.options_signal_generation_agent - INFO - [AI] Applying AI predictions for 1 signals in 1min timeframe... 🤖
2025-08-13 13:48:19,796 - agents.options_signal_generation_agent - INFO - [AI] Enhanced 1 signals with AI predictions
2025-08-13 13:48:19,796 - agents.options_signal_generation_agent - INFO - [CONFIDENCE] Calculating confidence scores for short_straddle in 1min... ⭐
2025-08-13 13:48:19,796 - agents.options_signal_generation_agent - INFO - [BACKTEST] Fetching historical performance for strategy 'short_straddle' (1min)... 📊
2025-08-13 13:48:19,796 - agents.options_signal_generation_agent - INFO - [MARKET] Fetching real-time market conditions... ⚡
2025-08-13 13:48:19,798 - agents.options_signal_generation_agent - INFO - [REGIME] Applying market regime filtering... 🚦
2025-08-13 13:48:19,798 - agents.options_signal_generation_agent - INFO - [FILTER] Signal short_straddle filtered out due to low confidence (0.42) after regime check. 📉
2025-08-13 13:48:19,798 - agents.options_signal_generation_agent - INFO - [RISK] Fetching risk management guidelines... 🛡️
2025-08-13 13:48:19,798 - agents.options_signal_generation_agent - INFO - [RISK] Applying risk management filtering... 🚨
2025-08-13 13:48:19,798 - agents.options_signal_generation_agent - INFO - [SIGNAL] All signals filtered out for strategy 'short_straddle' in 1min. 🗑️
2025-08-13 13:48:19,799 - agents.options_signal_generation_agent - INFO - [STRATEGY] Completed 20 strategy tasks for 1min timeframe
2025-08-13 13:48:19,799 - agents.options_signal_generation_agent - INFO - [SIGNAL] Total signals generated across all timeframes: 8 📈
2025-08-13 13:48:19,800 - agents.options_signal_generation_agent - INFO - [SIGNAL] Filtered to BUY signals only: 0 🛒
2025-08-13 13:48:19,800 - agents.options_signal_generation_agent - INFO - [MULTI-TF] Applying multi-timeframe confirmation logic... 🕰️
2025-08-13 13:48:19,800 - agents.options_signal_generation_agent - INFO - [SIGNAL] Final signals after multi-timeframe confirmation: 0 ✅
2025-08-13 13:48:19,800 - agents.options_signal_generation_agent - INFO - [COORDINATION] Checking for multi-agent overrides/suggestions... 🤝
2025-08-13 13:48:19,800 - agents.options_signal_generation_agent - INFO - [SIGNAL] Final signals after agent overrides: 0 🤝
2025-08-13 13:48:19,801 - agents.options_signal_generation_agent - INFO - [SIGNAL] No final consolidated signals after all filtering. 🗑️
2025-08-13 13:48:19,801 - agents.options_signal_generation_agent - INFO - [EXECUTION] ✅ Signal generation cycle completed in 14.39s
2025-08-13 13:48:19,801 - agents.options_signal_generation_agent - INFO - [EXECUTION] 📊 Buy signals generated: 0
2025-08-13 13:48:19,801 - agents.options_signal_generation_agent - INFO - [EXECUTION] ⏰ Next cycle in 10s
2025-08-13 13:48:19,801 - agents.options_signal_generation_agent - INFO - [SUCCESS] All signal generation tasks completed successfully ✅
2025-08-13 13:48:19,801 - agents.options_signal_generation_agent - INFO - [CONTINUOUS] Waiting 10 seconds before next cycle... ⏳
2025-08-13 13:48:22,880 - agents.options_market_monitoring_agent - WARNING - [WARNING] No technical indicators data available, using fallback
2025-08-13 13:48:22,881 - agents.options_market_monitoring_agent - INFO - 📊 [OUTPUT] Market summary saved to data\live\market_summary.json
2025-08-13 13:48:22,881 - agents.options_market_monitoring_agent - INFO - 🗣️ [LLM] Natural Language  Summary: Market snapshot at 2025-08-13 13:48:22:
The market is currently unknown with unknown volatility.
Recommended assets for focus: NIFTY, BANKNIFTY.

